# OpsGraph 使用指南

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置你的 OpenAI API 密钥
```

### 2. 配置 API 密钥

在 `.env` 文件中设置：

```bash
OPENAI_API_KEY=your_actual_openai_api_key_here
PROMETHEUS_URL=http://localhost:9090  # 可选，默认使用 mock 数据
```

### 3. 运行方式

#### 交互式模式（推荐）

```bash
python main.py
```

这将启动交互式界面，你可以：
- 输入自然语言查询
- 查看可用工具
- 切换元数据显示
- 获取帮助信息

#### 单次查询模式

```bash
python main.py query "查询 node-01 的 CPU 使用率"
```

#### 演示模式

```bash
python main.py demo
```

运行预设的演示查询，展示系统功能。

#### 系统信息

```bash
python main.py info
```

查看系统配置和状态。

## 📝 查询示例

### Prometheus 监控查询

```
查询 node-01 的 CPU 使用率过去 1 小时
显示所有节点的内存使用情况
过去 24 小时内服务的响应时间
磁盘使用率超过 80% 的服务器
```

### 日志查询（占位功能）

```
显示过去 1 小时的错误日志
查找包含 "database connection" 的日志
分析 service-api 的日志模式
```

### K8s 查询（占位功能）

```
显示 default 命名空间中的所有 Pod
获取 service-api 部署的状态
列出资源使用率高的节点
```

### 自动化操作（占位功能）

```
重启 service-api 服务
将前端服务扩展到 5 个副本
对所有服务执行健康检查
```

### 知识库查询（占位功能）

```
如何排查高 CPU 使用率问题？
显示数据库维护的操作手册
查找监控设置的最佳实践
```

## 🛠️ 高级功能

### 元数据显示

在交互模式中输入 `meta` 可以切换元数据显示，包括：
- 选择的工具
- 路由置信度
- 执行路径
- 错误信息

### 原始输出

使用 `--raw` 参数获取 JSON 格式的原始输出：

```bash
python main.py query "你的查询" --raw
```

### 调试模式

在 `.env` 文件中设置：

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## 🔧 故障排除

### 常见问题

1. **API 密钥错误**
   - 确保在 `.env` 文件中设置了正确的 `OPENAI_API_KEY`
   - 检查 API 密钥是否有效且有足够的配额

2. **Prometheus 连接失败**
   - 系统会自动使用 mock 数据
   - 如需连接真实 Prometheus，确保 `PROMETHEUS_URL` 正确

3. **依赖包问题**
   - 运行 `pip install -r requirements.txt` 重新安装依赖
   - 确保 Python 版本 >= 3.8

### 日志查看

系统日志会显示详细的执行信息，包括：
- 路由决策过程
- 工具执行状态
- 错误信息和堆栈跟踪

## 🏗️ 架构说明

### 核心组件

1. **LLMRouter**: 智能路由，分析用户意图并选择合适的工具
2. **工具层**: 包含 Prometheus 工具和其他占位工具
3. **LLMInterpreter**: 将工具结果转换为人类可读的响应
4. **LangGraph**: 协调整个工作流程

### 数据流

```
用户查询 → 路由器 → 工具执行 → 结果解释 → 最终响应
```

### 扩展性

- 添加新工具：在 `tools/` 目录下创建新的工具类
- 修改路由逻辑：更新 `agents/router.py`
- 自定义解释器：修改 `agents/interpreter.py`

## 📊 性能优化

### 缓存

- LLM 调用结果可以缓存以提高响应速度
- 配置设置使用 `@lru_cache` 装饰器

### 并发

- 支持异步操作（`ainvoke` 方法）
- 可以并行处理多个查询

## 🔒 安全考虑

- API 密钥通过环境变量管理
- 不在代码中硬编码敏感信息
- 工具执行有适当的错误处理和回退机制

## 📈 监控和指标

系统提供以下监控信息：
- 查询处理时间
- 工具选择准确率
- 错误率和类型
- 资源使用情况

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件
