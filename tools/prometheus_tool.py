"""
Prometheus Tool for OpsGraph platform.

This tool handles natural language to PromQL conversion and Prometheus API queries.
"""

import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin

from langchain_core.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage

from config.settings import get_settings

logger = logging.getLogger(__name__)


class PrometheusTool(BaseTool):
    """Tool for querying Prometheus metrics using natural language."""

    name: str = "prometheus_query"
    description: str = """
    Query Prometheus metrics using natural language.
    Input should be a natural language description of what metrics you want to query.
    Examples:
    - "CPU usage for node-01 in the last hour"
    - "Memory utilization across all nodes"
    - "HTTP request rate for service-api"
    - "Disk usage percentage for all servers"
    """

    def __init__(self):
        super().__init__()
        self._settings = None
        self._prometheus_url = None
        self._timeout = None
        self._llm = None

    @property
    def settings(self):
        if self._settings is None:
            self._settings = get_settings()
        return self._settings

    @property
    def prometheus_url(self):
        if self._prometheus_url is None:
            self._prometheus_url = self.settings.prometheus_url
        return self._prometheus_url

    @property
    def timeout(self):
        if self._timeout is None:
            self._timeout = self.settings.prometheus_timeout
        return self._timeout

    @property
    def llm(self):
        if self._llm is None:
            self._llm = ChatOpenAI(
                model=self.settings.openai_model,
                temperature=self.settings.openai_temperature,
                api_key=self.settings.openai_api_key
            )
        return self._llm
    
    def _run(self, query: str) -> str:
        """Execute the tool with the given natural language query."""
        try:
            logger.info(f"Processing Prometheus query: {query}")
            
            # Step 1: Convert natural language to PromQL
            promql_query = self._nl_to_promql(query)
            logger.info(f"Generated PromQL: {promql_query}")
            
            # Step 2: Execute PromQL query against Prometheus
            result = self._execute_promql(promql_query)
            
            # Step 3: Format result for return
            formatted_result = {
                "original_query": query,
                "promql_query": promql_query,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(formatted_result, indent=2)
            
        except Exception as e:
            logger.error(f"Error in PrometheusTool: {str(e)}")
            return json.dumps({
                "error": str(e),
                "original_query": query,
                "timestamp": datetime.now().isoformat()
            })
    
    async def _arun(self, query: str) -> str:
        """Async version of the tool execution."""
        # For now, just call the sync version
        return self._run(query)
    
    def _nl_to_promql(self, natural_language_query: str) -> str:
        """Convert natural language query to PromQL using LLM."""
        
        system_prompt = """
        You are an expert in Prometheus and PromQL. Your task is to convert natural language queries 
        into valid PromQL queries. 
        
        Common Prometheus metrics patterns:
        - CPU usage: cpu_usage_percent, node_cpu_seconds_total
        - Memory usage: memory_usage_percent, node_memory_MemAvailable_bytes
        - Disk usage: disk_usage_percent, node_filesystem_avail_bytes
        - Network: network_receive_bytes_total, network_transmit_bytes_total
        - HTTP requests: http_requests_total, http_request_duration_seconds
        - Container metrics: container_cpu_usage_seconds_total, container_memory_usage_bytes
        
        Time ranges:
        - "last hour" -> [1h]
        - "past 5 minutes" -> [5m]
        - "last day" -> [1d]
        
        Aggregations:
        - "average" -> avg()
        - "maximum" -> max()
        - "total" -> sum()
        - "rate" -> rate()
        
        Only return the PromQL query, nothing else.
        """
        
        user_prompt = f"Convert this natural language query to PromQL: {natural_language_query}"
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = self.llm.invoke(messages)
            promql_query = response.content.strip()
            
            # Clean up the response (remove any markdown formatting)
            if promql_query.startswith("```"):
                lines = promql_query.split("\n")
                promql_query = "\n".join(lines[1:-1])
            
            return promql_query
            
        except Exception as e:
            logger.error(f"Error converting NL to PromQL: {str(e)}")
            # Fallback to a simple query
            return 'up'
    
    def _execute_promql(self, promql_query: str) -> Dict[str, Any]:
        """Execute PromQL query against Prometheus API."""
        
        try:
            # Prepare the API endpoint
            api_url = urljoin(self.prometheus_url, "/api/v1/query")
            
            # Prepare query parameters
            params = {
                "query": promql_query,
                "time": datetime.now().isoformat()
            }
            
            # Execute the query
            response = requests.get(
                api_url,
                params=params,
                timeout=self.timeout,
                headers={"Accept": "application/json"}
            )
            
            response.raise_for_status()
            data = response.json()
            
            if data.get("status") == "success":
                return {
                    "status": "success",
                    "data": data.get("data", {}),
                    "query_time": data.get("data", {}).get("resultType", "unknown")
                }
            else:
                return {
                    "status": "error",
                    "error": data.get("error", "Unknown error"),
                    "errorType": data.get("errorType", "unknown")
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"HTTP error querying Prometheus: {str(e)}")
            return self._get_mock_data(promql_query)
        except Exception as e:
            logger.error(f"Error executing PromQL: {str(e)}")
            return self._get_mock_data(promql_query)
    
    def _get_mock_data(self, promql_query: str) -> Dict[str, Any]:
        """Generate mock data when Prometheus is not available."""
        logger.warning("Using mock data - Prometheus not available")
        
        # Generate realistic mock data based on query type
        if "cpu" in promql_query.lower():
            mock_data = {
                "resultType": "vector",
                "result": [
                    {
                        "metric": {"instance": "node-01:9100", "job": "node"},
                        "value": [datetime.now().timestamp(), "45.2"]
                    },
                    {
                        "metric": {"instance": "node-02:9100", "job": "node"},
                        "value": [datetime.now().timestamp(), "32.8"]
                    }
                ]
            }
        elif "memory" in promql_query.lower():
            mock_data = {
                "resultType": "vector",
                "result": [
                    {
                        "metric": {"instance": "node-01:9100", "job": "node"},
                        "value": [datetime.now().timestamp(), "78.5"]
                    },
                    {
                        "metric": {"instance": "node-02:9100", "job": "node"},
                        "value": [datetime.now().timestamp(), "65.2"]
                    }
                ]
            }
        else:
            mock_data = {
                "resultType": "vector",
                "result": [
                    {
                        "metric": {"instance": "localhost:9090", "job": "prometheus"},
                        "value": [datetime.now().timestamp(), "1"]
                    }
                ]
            }
        
        return {
            "status": "success",
            "data": mock_data,
            "mock": True,
            "note": "This is mock data - Prometheus server not available"
        }
