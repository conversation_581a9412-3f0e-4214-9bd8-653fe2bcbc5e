# OpenAI API Configuration
OPENAI_API_KEY=csk-ckf44rk3c249xd5vn8cy6jn8hkwdmjnr6mfh28c83hhm4mtc
OPENAI_MODEL=gpt-oss-120b
OPENAI_TEMPERATURE=0.1
OPENAI_BASE_URL=https://api.cerebras.ai/

# Prometheus Configuration
PROMETHEUS_URL=http://192.168.2.52:19090/
PROMETHEUS_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Application Configuration
APP_NAME=OpsGraph
APP_VERSION=1.0.0
DEBUG=false
