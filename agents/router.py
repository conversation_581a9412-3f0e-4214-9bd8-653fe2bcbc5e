"""
LLM Router Agent for OpsGraph platform.

This agent handles intent recognition and routes user queries to appropriate tools.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from enum import Enum

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from config.settings import get_settings

logger = logging.getLogger(__name__)


class ToolType(str, Enum):
    """Available tool types for routing."""
    PROMETHEUS = "prometheus"
    LOG = "log"
    CICD = "cicd"
    K8S = "k8s"
    AUTOMATION = "automation"
    KNOWLEDGE = "knowledge"


class RouterDecision(BaseModel):
    """Router decision model."""
    tool_type: ToolType = Field(..., description="The tool to use for this query")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)")
    reasoning: str = Field(..., description="Explanation for the routing decision")
    processed_query: str = Field(..., description="Processed/cleaned query for the tool")


class LLMRouter:
    """LLM-based router for intent recognition and tool selection."""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm = ChatOpenAI(
            # model=self.settings.openai_model,
            temperature=0.1,  # Low temperature for consistent routing
            api_key=self.settings.openai_api_key,
            base_url=self.settings.openai_base_url
        )
        
        # Tool descriptions for routing decisions
        self.tool_descriptions = {
            ToolType.PROMETHEUS: {
                "description": "Query Prometheus metrics and monitoring data",
                "keywords": ["cpu", "memory", "disk", "network", "metrics", "usage", "utilization", 
                           "performance", "load", "throughput", "latency", "response time", "prometheus"],
                "examples": [
                    "CPU usage for node-01",
                    "Memory utilization across all nodes", 
                    "HTTP request rate for service-api",
                    "Disk usage percentage"
                ]
            },
            ToolType.LOG: {
                "description": "Query and analyze application logs",
                "keywords": ["logs", "error", "exception", "debug", "trace", "audit", "events",
                           "log analysis", "log search", "error logs"],
                "examples": [
                    "Show error logs from the last hour",
                    "Find logs containing database errors",
                    "Analyze log patterns for service-api"
                ]
            },
            ToolType.CICD: {
                "description": "Query CI/CD pipelines and deployment information",
                "keywords": ["build", "deploy", "pipeline", "ci/cd", "deployment", "release",
                           "build status", "deployment status", "pipeline failure"],
                "examples": [
                    "Show recent build failures",
                    "Get deployment status for service-api",
                    "List running pipelines"
                ]
            },
            ToolType.K8S: {
                "description": "Query Kubernetes cluster resources and status",
                "keywords": ["kubernetes", "k8s", "pod", "deployment", "service", "namespace",
                           "node", "cluster", "container", "replica", "kubectl"],
                "examples": [
                    "Show all pods in default namespace",
                    "Get status of deployment service-api",
                    "List nodes with high resource usage"
                ]
            },
            ToolType.AUTOMATION: {
                "description": "Execute automation tasks and orchestration",
                "keywords": ["restart", "scale", "execute", "run", "automation", "orchestration",
                           "maintenance", "health check", "remediation"],
                "examples": [
                    "Restart service-api deployment",
                    "Scale up frontend service",
                    "Run health check on all services"
                ]
            },
            ToolType.KNOWLEDGE: {
                "description": "Query knowledge base and documentation",
                "keywords": ["how to", "documentation", "guide", "runbook", "troubleshooting",
                           "best practices", "help", "manual", "wiki", "knowledge"],
                "examples": [
                    "How to troubleshoot high CPU usage?",
                    "Show runbook for database maintenance",
                    "Find documentation about monitoring"
                ]
            }
        }
    
    def route_query(self, user_query: str) -> RouterDecision:
        """Route user query to appropriate tool."""
        try:
            logger.info(f"Routing query: {user_query}")
            
            # Use LLM for intelligent routing
            decision = self._llm_route(user_query)
            
            logger.info(f"Routing decision: {decision.tool_type} (confidence: {decision.confidence})")
            return decision
            
        except Exception as e:
            logger.error(f"Error in routing: {str(e)}")
            # Fallback to default routing
            return self._fallback_route(user_query)
    
    def _llm_route(self, user_query: str) -> RouterDecision:
        """Use LLM to make routing decision."""
        
        # Create tool descriptions for the prompt
        tool_info = []
        for tool_type, info in self.tool_descriptions.items():
            tool_info.append(f"""
{tool_type.value.upper()}:
- Description: {info['description']}
- Keywords: {', '.join(info['keywords'])}
- Examples: {'; '.join(info['examples'])}
""")
        
        system_prompt = f"""
You are an intelligent router for an operations platform. Your job is to analyze user queries 
and determine which tool should handle them.

Available tools:
{''.join(tool_info)}

Analyze the user query and return a JSON response with:
1. tool_type: The most appropriate tool (one of: {', '.join([t.value for t in ToolType])})
2. confidence: A confidence score from 0.0 to 1.0
3. reasoning: Brief explanation for your choice
4. processed_query: The query cleaned/optimized for the chosen tool

Consider:
- Keywords and context in the query
- The primary intent of the user
- Which tool would best fulfill the request

Return only valid JSON, no other text.
"""
        
        user_prompt = f"Route this query: {user_query}"
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = self.llm.invoke(messages)
            response_text = response.content.strip()
            
            # Clean up response if it has markdown formatting
            if response_text.startswith("```json"):
                response_text = response_text[7:-3].strip()
            elif response_text.startswith("```"):
                response_text = response_text[3:-3].strip()
            
            # Parse JSON response
            decision_data = json.loads(response_text)
            
            # Validate and create RouterDecision
            return RouterDecision(
                tool_type=ToolType(decision_data["tool_type"]),
                confidence=float(decision_data["confidence"]),
                reasoning=decision_data["reasoning"],
                processed_query=decision_data["processed_query"]
            )
            
        except Exception as e:
            logger.error(f"Error in LLM routing: {str(e)}")
            return self._fallback_route(user_query)
    
    def _fallback_route(self, user_query: str) -> RouterDecision:
        """Fallback routing using keyword matching."""
        query_lower = user_query.lower()
        
        # Score each tool based on keyword matches
        scores = {}
        for tool_type, info in self.tool_descriptions.items():
            score = 0
            for keyword in info["keywords"]:
                if keyword.lower() in query_lower:
                    score += 1
            scores[tool_type] = score
        
        # Find the tool with highest score
        best_tool = max(scores, key=scores.get)
        max_score = scores[best_tool]
        
        # Calculate confidence based on keyword matches
        total_keywords = sum(len(info["keywords"]) for info in self.tool_descriptions.values())
        confidence = min(max_score / 5.0, 1.0)  # Normalize to 0-1 range
        
        # Default to Prometheus if no clear match
        if confidence < 0.2:
            best_tool = ToolType.PROMETHEUS
            confidence = 0.3
            reasoning = "No clear tool match found, defaulting to Prometheus for metrics queries"
        else:
            reasoning = f"Keyword-based routing with {max_score} keyword matches"
        
        return RouterDecision(
            tool_type=best_tool,
            confidence=confidence,
            reasoning=reasoning,
            processed_query=user_query
        )
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools with descriptions."""
        tools = []
        for tool_type, info in self.tool_descriptions.items():
            tools.append({
                "name": tool_type.value,
                "description": info["description"],
                "examples": info["examples"]
            })
        return tools
