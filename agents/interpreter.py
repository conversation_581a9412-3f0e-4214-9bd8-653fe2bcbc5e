"""
LLM Interpreter Agent for OpsGraph platform.

This agent interprets and summarizes tool results into human-readable responses.
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel, Field

from config.settings import get_settings

logger = logging.getLogger(__name__)


class InterpretationResult(BaseModel):
    """Result of interpretation process."""
    summary: str = Field(..., description="Human-readable summary of the results")
    key_insights: list = Field(..., description="Key insights extracted from the data")
    recommendations: Optional[list] = Field(None, description="Actionable recommendations")
    raw_data_included: bool = Field(..., description="Whether raw data is included in response")


class LLMInterpreter:
    """LLM-based interpreter for converting tool results to human-readable responses."""
    
    def __init__(self):
        self.settings = get_settings()
        self.llm = ChatOpenAI(
            # model=self.settings.openai_model,
            temperature=0.2,  # Slightly higher for more natural language
            api_key=self.settings.openai_api_key,
            base_url=self.settings.openai_base_url
        )
    
    def interpret_results(
        self, 
        original_query: str, 
        tool_name: str, 
        tool_results: str,
        include_raw_data: bool = False
    ) -> str:
        """Interpret tool results and generate human-readable response."""
        
        try:
            logger.info(f"Interpreting results from {tool_name} for query: {original_query}")
            
            # Parse tool results if it's JSON
            try:
                parsed_results = json.loads(tool_results)
            except json.JSONDecodeError:
                parsed_results = {"raw_output": tool_results}
            
            # Generate interpretation based on tool type
            interpretation = self._generate_interpretation(
                original_query, tool_name, parsed_results, include_raw_data
            )
            
            return interpretation
            
        except Exception as e:
            logger.error(f"Error in interpretation: {str(e)}")
            return self._generate_error_response(original_query, tool_name, str(e))
    
    def _generate_interpretation(
        self, 
        original_query: str, 
        tool_name: str, 
        results: Dict[str, Any],
        include_raw_data: bool
    ) -> str:
        """Generate interpretation using LLM."""
        
        # Create tool-specific context
        tool_context = self._get_tool_context(tool_name)
        
        # Prepare the results summary
        results_summary = self._prepare_results_summary(results, tool_name)
        
        system_prompt = f"""
You are an expert operations analyst. Your job is to interpret technical data and present it 
in a clear, actionable way for operations teams.

Context:
- Tool used: {tool_name}
- Tool purpose: {tool_context}
- Original user query: "{original_query}"

Your response should:
1. Provide a clear, concise summary of what the data shows
2. Highlight key insights and important findings
3. Identify any potential issues or anomalies
4. Suggest actionable next steps when appropriate
5. Use plain language that both technical and non-technical users can understand

Format your response as a well-structured analysis with:
- **Summary**: Brief overview of findings
- **Key Insights**: Important observations from the data
- **Recommendations**: Actionable next steps (if applicable)
- **Additional Details**: Technical details (if relevant)

Be specific with numbers, timestamps, and metrics when available.
"""
        
        user_prompt = f"""
Analyze and interpret these results:

{results_summary}

Provide a comprehensive analysis that answers the user's original question: "{original_query}"
"""
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        try:
            response = self.llm.invoke(messages)
            interpretation = response.content.strip()
            
            # Add raw data if requested
            if include_raw_data:
                interpretation += "\n\n---\n**Raw Data:**\n```json\n"
                interpretation += json.dumps(results, indent=2)
                interpretation += "\n```"
            
            return interpretation
            
        except Exception as e:
            logger.error(f"Error generating interpretation: {str(e)}")
            return self._generate_fallback_interpretation(original_query, tool_name, results)
    
    def _get_tool_context(self, tool_name: str) -> str:
        """Get context description for different tools."""
        contexts = {
            "prometheus_query": "Queries Prometheus metrics for monitoring and performance data",
            "log_query": "Searches and analyzes application logs for troubleshooting",
            "cicd_query": "Retrieves CI/CD pipeline and deployment information",
            "k8s_query": "Queries Kubernetes cluster resources and status",
            "automation_query": "Executes automation tasks and orchestration",
            "knowledge_query": "Searches knowledge base and documentation"
        }
        return contexts.get(tool_name, "Performs operations-related queries")
    
    def _prepare_results_summary(self, results: Dict[str, Any], tool_name: str) -> str:
        """Prepare a structured summary of results for the LLM."""
        
        if tool_name == "prometheus_query":
            return self._summarize_prometheus_results(results)
        elif tool_name in ["log_query", "cicd_query", "k8s_query", "automation_query", "knowledge_query"]:
            return self._summarize_generic_results(results)
        else:
            return json.dumps(results, indent=2)
    
    def _summarize_prometheus_results(self, results: Dict[str, Any]) -> str:
        """Summarize Prometheus query results."""
        summary_parts = []
        
        if "original_query" in results:
            summary_parts.append(f"Original Query: {results['original_query']}")
        
        if "promql_query" in results:
            summary_parts.append(f"PromQL Query: {results['promql_query']}")
        
        if "result" in results:
            result_data = results["result"]
            
            if result_data.get("status") == "success":
                data = result_data.get("data", {})
                if "result" in data:
                    metrics = data["result"]
                    summary_parts.append(f"Number of metrics returned: {len(metrics)}")
                    
                    # Summarize metric values
                    if metrics:
                        summary_parts.append("Metric values:")
                        for i, metric in enumerate(metrics[:5]):  # Show first 5 metrics
                            instance = metric.get("metric", {}).get("instance", "unknown")
                            value = metric.get("value", [None, "N/A"])[1]
                            summary_parts.append(f"  - {instance}: {value}")
                        
                        if len(metrics) > 5:
                            summary_parts.append(f"  ... and {len(metrics) - 5} more metrics")
            
            elif result_data.get("status") == "error":
                summary_parts.append(f"Error: {result_data.get('error', 'Unknown error')}")
            
            if result_data.get("mock"):
                summary_parts.append("Note: Using mock data (Prometheus not available)")
        
        return "\n".join(summary_parts)
    
    def _summarize_generic_results(self, results: Dict[str, Any]) -> str:
        """Summarize results from other tools."""
        summary_parts = []
        
        if "tool" in results:
            summary_parts.append(f"Tool: {results['tool']}")
        
        if "query" in results:
            summary_parts.append(f"Query: {results['query']}")
        
        if "status" in results:
            summary_parts.append(f"Status: {results['status']}")
        
        # Add specific data based on tool type
        data_keys = ["logs", "pipelines", "resources", "tasks", "articles"]
        for key in data_keys:
            if key in results:
                data = results[key]
                if isinstance(data, list):
                    summary_parts.append(f"Number of {key}: {len(data)}")
                    if data:
                        summary_parts.append(f"Sample {key[:-1]}:")
                        summary_parts.append(json.dumps(data[0], indent=2))
        
        if "message" in results:
            summary_parts.append(f"Message: {results['message']}")
        
        return "\n".join(summary_parts)
    
    def _generate_fallback_interpretation(
        self, 
        original_query: str, 
        tool_name: str, 
        results: Dict[str, Any]
    ) -> str:
        """Generate a basic interpretation when LLM fails."""
        
        interpretation = f"## Results for: {original_query}\n\n"
        interpretation += f"**Tool Used:** {tool_name}\n\n"
        
        if results.get("status") == "success" or results.get("status") == "mock_success":
            interpretation += "**Status:** ✅ Query executed successfully\n\n"
            
            # Add basic data summary
            if "result" in results and isinstance(results["result"], dict):
                data = results["result"].get("data", {})
                if "result" in data and isinstance(data["result"], list):
                    interpretation += f"**Results:** Found {len(data['result'])} metrics\n\n"
            
            interpretation += "**Summary:** The query was processed and returned data. "
            interpretation += "Please review the results for specific values and insights.\n\n"
            
        else:
            interpretation += "**Status:** ❌ Query encountered an issue\n\n"
            if "error" in results:
                interpretation += f"**Error:** {results['error']}\n\n"
        
        interpretation += "**Raw Results:**\n```json\n"
        interpretation += json.dumps(results, indent=2)
        interpretation += "\n```"
        
        return interpretation
    
    def _generate_error_response(self, original_query: str, tool_name: str, error: str) -> str:
        """Generate error response when interpretation fails."""
        return f"""
## Error Processing Query: {original_query}

**Tool:** {tool_name}
**Error:** {error}
**Timestamp:** {datetime.now().isoformat()}

The system encountered an error while processing your query. Please try rephrasing your question or contact support if the issue persists.
"""
