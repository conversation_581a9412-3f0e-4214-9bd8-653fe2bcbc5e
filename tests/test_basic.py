"""
Basic tests for OpsGraph platform components.
"""

import pytest
import json
import os
import sys

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.router import LLMRouter, ToolType
from agents.interpreter import LLMInterpreter
from tools.prometheus_tool import PrometheusTool
from tools.placeholder_tools import LogTool, CICDTool, K8sTool
from graphs.ops_agent import OpsAgentGraph


class TestRouter:
    """Test cases for LLMRouter."""
    
    def test_router_initialization(self):
        """Test router can be initialized."""
        router = LLMRouter()
        assert router is not None
        assert router.llm is not None
    
    def test_get_available_tools(self):
        """Test getting available tools list."""
        router = LLMRouter()
        tools = router.get_available_tools()
        
        assert isinstance(tools, list)
        assert len(tools) > 0
        
        # Check tool structure
        for tool in tools:
            assert "name" in tool
            assert "description" in tool
            assert "examples" in tool
    
    def test_fallback_routing(self):
        """Test fallback routing with keyword matching."""
        router = LLMRouter()
        
        # Test CPU query (should route to Prometheus)
        decision = router._fallback_route("show CPU usage")
        assert decision.tool_type == ToolType.PROMETHEUS
        assert decision.confidence > 0
        
        # Test log query
        decision = router._fallback_route("show error logs")
        assert decision.tool_type == ToolType.LOG
        
        # Test K8s query
        decision = router._fallback_route("list all pods")
        assert decision.tool_type == ToolType.K8S


class TestTools:
    """Test cases for tools."""
    
    def test_prometheus_tool_initialization(self):
        """Test Prometheus tool initialization."""
        tool = PrometheusTool()
        assert tool.name == "prometheus_query"
        assert tool.description is not None
    
    def test_prometheus_tool_mock_execution(self):
        """Test Prometheus tool with mock data."""
        tool = PrometheusTool()
        result = tool._run("CPU usage for node-01")
        
        # Should return JSON string
        assert isinstance(result, str)
        
        # Should be valid JSON
        parsed = json.loads(result)
        assert "original_query" in parsed
        assert "result" in parsed
    
    def test_placeholder_tools(self):
        """Test placeholder tools."""
        tools = [LogTool(), CICDTool(), K8sTool()]
        
        for tool in tools:
            result = tool._run("test query")
            assert isinstance(result, str)
            
            # Should be valid JSON
            parsed = json.loads(result)
            assert "tool" in parsed
            assert "query" in parsed


class TestInterpreter:
    """Test cases for LLMInterpreter."""
    
    def test_interpreter_initialization(self):
        """Test interpreter initialization."""
        interpreter = LLMInterpreter()
        assert interpreter is not None
        assert interpreter.llm is not None
    
    def test_error_response_generation(self):
        """Test error response generation."""
        interpreter = LLMInterpreter()
        
        error_response = interpreter._generate_error_response(
            "test query", "test_tool", "test error"
        )
        
        assert isinstance(error_response, str)
        assert "test query" in error_response
        assert "test_tool" in error_response
        assert "test error" in error_response
    
    def test_fallback_interpretation(self):
        """Test fallback interpretation."""
        interpreter = LLMInterpreter()
        
        mock_results = {
            "status": "success",
            "result": {"data": {"result": [{"metric": {}, "value": ["123", "45.6"]}]}}
        }
        
        interpretation = interpreter._generate_fallback_interpretation(
            "test query", "prometheus_query", mock_results
        )
        
        assert isinstance(interpretation, str)
        assert "test query" in interpretation
        assert "prometheus_query" in interpretation


class TestOpsAgentGraph:
    """Test cases for OpsAgentGraph."""
    
    def test_graph_initialization(self):
        """Test graph initialization."""
        try:
            agent = OpsAgentGraph()
            assert agent is not None
            assert agent.router is not None
            assert agent.interpreter is not None
            assert len(agent.tools) > 0
        except Exception as e:
            # Skip if configuration is missing
            pytest.skip(f"Configuration missing: {e}")
    
    def test_get_available_tools(self):
        """Test getting available tools from graph."""
        try:
            agent = OpsAgentGraph()
            tools = agent.get_available_tools()
            
            assert isinstance(tools, list)
            assert len(tools) > 0
        except Exception as e:
            pytest.skip(f"Configuration missing: {e}")
    
    def test_graph_visualization(self):
        """Test graph visualization."""
        try:
            agent = OpsAgentGraph()
            viz = agent.get_graph_visualization()
            
            assert isinstance(viz, str)
            assert "OpsAgent Graph Structure" in viz
        except Exception as e:
            pytest.skip(f"Configuration missing: {e}")


class TestIntegration:
    """Integration tests."""
    
    def test_end_to_end_mock(self):
        """Test end-to-end flow with mock data."""
        try:
            agent = OpsAgentGraph()
            
            # Test with a simple query
            result = agent.invoke("show CPU usage")
            
            assert isinstance(result, dict)
            assert "query" in result
            assert "response" in result
            assert "metadata" in result
            
            metadata = result["metadata"]
            assert "selected_tool" in metadata
            assert "execution_path" in metadata
            
        except Exception as e:
            pytest.skip(f"Configuration missing: {e}")


if __name__ == "__main__":
    # Run basic tests
    print("Running basic tests...")
    
    # Test router
    print("Testing router...")
    router = LLMRouter()
    tools = router.get_available_tools()
    print(f"Available tools: {len(tools)}")
    
    # Test tools
    print("Testing tools...")
    prometheus_tool = PrometheusTool()
    result = prometheus_tool._run("CPU usage")
    print("Prometheus tool test passed")
    
    # Test interpreter
    print("Testing interpreter...")
    interpreter = LLMInterpreter()
    print("Interpreter test passed")
    
    print("All basic tests completed!")
