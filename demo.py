#!/usr/bin/env python3
"""
OpsGraph Demo Script

演示 OpsGraph 平台的核心功能。
"""

import os
import sys
import json
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """测试配置是否正确。"""
    print("🔧 测试配置...")
    
    try:
        from config.settings import get_settings, validate_settings
        settings = get_settings()
        validate_settings(settings)
        print("✅ 配置验证成功")
        return True
    except Exception as e:
        print(f"❌ 配置错误: {e}")
        print("\n💡 解决方案:")
        print("1. 复制 .env.example 到 .env")
        print("2. 设置 OPENAI_API_KEY=your_api_key")
        print("3. 可选：设置 PROMETHEUS_URL (默认使用 mock 数据)")
        return False


def test_components():
    """测试各个组件。"""
    print("\n🧪 测试组件...")
    
    try:
        # Test router
        print("  📍 测试路由器...")
        from agents.router import LLMRouter
        router = LLMRouter()
        decision = router._fallback_route("show CPU usage")
        print(f"    路由决策: {decision.tool_type} (置信度: {decision.confidence:.2f})")
        
        # Test tools
        print("  🔧 测试工具...")
        from tools.prometheus_tool import PrometheusTool
        from tools.placeholder_tools import LogTool
        
        prometheus_tool = PrometheusTool()
        log_tool = LogTool()
        
        print("    Prometheus 工具初始化成功")
        print("    日志工具初始化成功")
        
        # Test interpreter
        print("  🤖 测试解释器...")
        from agents.interpreter import LLMInterpreter
        interpreter = LLMInterpreter()
        print("    解释器初始化成功")
        
        print("✅ 所有组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False


def test_graph():
    """测试图结构。"""
    print("\n📊 测试图结构...")
    
    try:
        from graphs.ops_agent import OpsAgentGraph
        agent = OpsAgentGraph()
        
        print("  图初始化成功")
        
        # Show available tools
        tools = agent.get_available_tools()
        print(f"  可用工具数量: {len(tools)}")
        
        # Show graph structure
        print("\n  图结构:")
        viz = agent.get_graph_visualization()
        print(viz)
        
        print("✅ 图结构测试通过")
        return agent
        
    except Exception as e:
        print(f"❌ 图结构测试失败: {e}")
        return None


def run_demo_queries(agent):
    """运行演示查询。"""
    print("\n🎬 运行演示查询...")
    
    demo_queries = [
        "查询 node-01 的 CPU 使用率过去 1 小时",
        "显示所有节点的内存使用情况", 
        "过去 24 小时内哪些服务出现了错误",
        "重启 service-api 服务",
        "如何排查高 CPU 使用率问题？"
    ]
    
    results = []
    
    for i, query in enumerate(demo_queries, 1):
        print(f"\n📝 演示 {i}/{len(demo_queries)}: {query}")
        print("   处理中...")
        
        try:
            result = agent.invoke(query)
            
            # Show basic info
            metadata = result.get("metadata", {})
            print(f"   ✅ 完成 - 工具: {metadata.get('selected_tool', '未知')}")
            print(f"   置信度: {metadata.get('tool_confidence', 0):.2f}")
            
            # Show response preview
            response = result.get("response", "")
            preview = response[:200] + "..." if len(response) > 200 else response
            print(f"   响应预览: {preview}")
            
            results.append(result)
            
        except Exception as e:
            print(f"   ❌ 失败: {e}")
            results.append({"error": str(e), "query": query})
    
    return results


def save_demo_results(results):
    """保存演示结果。"""
    print("\n💾 保存演示结果...")
    
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"demo_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"   结果已保存到: {filename}")
        
    except Exception as e:
        print(f"   ❌ 保存失败: {e}")


def main():
    """主函数。"""
    print("🎯 OpsGraph 平台演示")
    print("=" * 50)
    
    # Test configuration
    if not test_configuration():
        return
    
    # Test components
    if not test_components():
        return
    
    # Test graph
    agent = test_graph()
    if not agent:
        return
    
    # Run demo queries
    results = run_demo_queries(agent)
    
    # Save results
    save_demo_results(results)
    
    print("\n🎉 演示完成!")
    print("\n📋 总结:")
    print(f"   - 成功查询: {len([r for r in results if 'error' not in r])}")
    print(f"   - 失败查询: {len([r for r in results if 'error' in r])}")
    
    print("\n💡 下一步:")
    print("   - 运行 'python main.py' 启动交互模式")
    print("   - 运行 'python main.py demo' 查看完整演示")
    print("   - 运行 'python main.py query \"你的问题\"' 执行单个查询")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 演示被中断")
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        sys.exit(1)
