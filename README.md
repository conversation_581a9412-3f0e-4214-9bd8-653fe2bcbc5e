
# OpsGraph - 基于 LangGraph 的运维 Agent 平台

## 🎯 项目简介

OpsGraph 是一个基于 LangGraph 的运维 Agent 平台，通过自然语言查询 Prometheus 数据，返回可读的结果。采用 LLM + 多工具协作的图结构 Agent 架构。

## 🏗️ 架构设计

```
UserQuery → LLMRouter → Tool Layer → LLMInterpreter → Response
```

### 核心组件

- **LLMRouter**: 意图识别，决定调用哪个工具
- **PrometheusTool**: NL2PromQL 转换和 Prometheus API 查询
- **LLMInterpreter**: 结果解释和总结
- **占位工具**: LogTool、CICDTool、K8sTool 等（未来扩展）

## 📁 项目结构

```
OpsGraph/
├── agents/                 # Agent 实现
│   ├── __init__.py
│   ├── router.py          # LLMRouter 实现
│   └── interpreter.py     # LLMInterpreter 实现
├── tools/                 # 工具层
│   ├── __init__.py
│   ├── prometheus_tool.py # Prometheus 查询工具
│   └── placeholder_tools.py # 占位工具
├── graphs/                # LangGraph 图定义
│   ├── __init__.py
│   └── ops_agent.py       # 主要的 Agent 图
├── config/                # 配置管理
│   ├── __init__.py
│   └── settings.py        # 配置设置
├── tests/                 # 测试用例
│   └── __init__.py
├── main.py               # 程序入口
├── requirements.txt      # 依赖包
├── .env.example         # 环境变量示例
└── README.md           # 项目说明
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，设置 OPENAI_API_KEY 和 PROMETHEUS_URL
```

### 3. 运行 Demo

```bash
python main.py
```

## 📝 使用示例

```python
# 示例查询
"查询 node-01 的 CPU 使用率过去 1 小时"
"显示所有节点的内存使用情况"
"过去 24 小时内哪些服务出现了错误"
```

## 🔧 配置说明

在 `.env` 文件中配置以下参数：

- `OPENAI_API_KEY`: OpenAI API 密钥
- `PROMETHEUS_URL`: Prometheus 服务器地址
- `LOG_LEVEL`: 日志级别（默认: INFO）

## 🛠️ 开发指南

### 添加新工具

1. 在 `tools/` 目录下创建新的工具文件
2. 继承基础工具类并实现必要方法
3. 在 `graphs/ops_agent.py` 中注册新工具
4. 更新 `agents/router.py` 中的路由逻辑

### 扩展 Agent 功能

1. 修改 `graphs/ops_agent.py` 中的图结构
2. 添加新的节点和边
3. 更新相应的 Agent 实现

## 📄 许可证

MIT License
