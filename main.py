#!/usr/bin/env python3
"""
OpsGraph - 基于 LangGraph 的运维 Agent 平台

主程序入口，提供 CLI 和交互式界面。
"""

import os
import sys
import logging
from typing import Optional
from datetime import datetime

import typer
from rich.console import Console
from rich.panel import Panel
from rich.markdown import Markdown
from rich.prompt import Prompt
from rich.table import Table
from rich import print as rprint

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import get_settings, validate_settings
from graphs.ops_agent import OpsAgentGraph

# Initialize components
console = Console()
app = typer.Typer(help="OpsGraph - 基于 LangGraph 的运维 Agent 平台")
logger = logging.getLogger(__name__)


def setup_environment():
    """Setup environment and validate configuration."""
    try:
        settings = get_settings()
        validate_settings(settings)
        return settings
    except Exception as e:
        console.print(f"[red]配置错误: {e}[/red]")
        console.print("\n[yellow]请检查以下配置:[/yellow]")
        console.print("1. 复制 .env.example 到 .env")
        console.print("2. 设置 OPENAI_API_KEY")
        console.print("3. 设置 PROMETHEUS_URL (可选，默认使用 mock 数据)")
        sys.exit(1)


def display_welcome():
    """Display welcome message and system info."""
    welcome_text = """
# 🎯 OpsGraph - 运维 Agent 平台

基于 LangGraph 的智能运维助手，通过自然语言查询运维数据。

## 🚀 功能特性
- **智能路由**: 自动识别查询意图，选择合适的工具
- **Prometheus 集成**: 查询监控指标和性能数据
- **自然语言交互**: 使用中文或英文进行查询
- **可扩展架构**: 支持日志、CI/CD、K8s 等工具扩展

## 📝 查询示例
- "查询 node-01 的 CPU 使用率过去 1 小时"
- "显示所有节点的内存使用情况"
- "过去 24 小时内哪些服务出现了错误"
"""
    
    console.print(Panel(Markdown(welcome_text), title="欢迎使用 OpsGraph", border_style="blue"))


def display_available_tools(agent: OpsAgentGraph):
    """Display available tools and their descriptions."""
    tools = agent.get_available_tools()
    
    table = Table(title="可用工具", show_header=True, header_style="bold magenta")
    table.add_column("工具名称", style="cyan", no_wrap=True)
    table.add_column("描述", style="white")
    table.add_column("示例", style="green")
    
    for tool in tools:
        examples = "\n".join(tool["examples"][:2])  # Show first 2 examples
        table.add_row(tool["name"], tool["description"], examples)
    
    console.print(table)


def process_query(agent: OpsAgentGraph, query: str, show_metadata: bool = False):
    """Process a single query and display results."""
    try:
        console.print(f"\n[blue]🔍 处理查询:[/blue] {query}")
        
        with console.status("[bold green]正在处理查询..."):
            result = agent.invoke(query)
        
        # Display response
        response_text = result.get("response", "无响应")
        console.print(Panel(
            Markdown(response_text), 
            title="📊 查询结果", 
            border_style="green"
        ))
        
        # Display metadata if requested
        if show_metadata:
            metadata = result.get("metadata", {})
            
            meta_table = Table(title="执行元数据", show_header=True)
            meta_table.add_column("属性", style="cyan")
            meta_table.add_column("值", style="white")
            
            meta_table.add_row("选择的工具", str(metadata.get("selected_tool", "未知")))
            meta_table.add_row("置信度", f"{metadata.get('tool_confidence', 0):.2f}")
            meta_table.add_row("路由原因", str(metadata.get("routing_reasoning", "无")))
            meta_table.add_row("执行路径", " → ".join(metadata.get("execution_path", [])))
            meta_table.add_row("是否有错误", "是" if metadata.get("has_error") else "否")
            
            console.print(meta_table)
        
        return result
        
    except Exception as e:
        console.print(f"[red]❌ 查询处理失败: {e}[/red]")
        logger.error(f"Query processing error: {e}")
        return None


@app.command()
def query(
    text: str = typer.Argument(..., help="要查询的问题"),
    metadata: bool = typer.Option(False, "--metadata", "-m", help="显示执行元数据"),
    raw: bool = typer.Option(False, "--raw", "-r", help="显示原始输出")
):
    """执行单个查询"""
    settings = setup_environment()
    agent = OpsAgentGraph()
    
    if not raw:
        display_welcome()
    
    result = process_query(agent, text, show_metadata=metadata)
    
    if raw and result:
        # Output raw JSON for scripting
        import json
        print(json.dumps(result, indent=2, ensure_ascii=False))


@app.command()
def interactive():
    """启动交互式模式"""
    settings = setup_environment()
    agent = OpsAgentGraph()
    
    display_welcome()
    display_available_tools(agent)
    
    console.print("\n[yellow]💡 提示:[/yellow]")
    console.print("- 输入 'help' 查看帮助")
    console.print("- 输入 'tools' 查看可用工具")
    console.print("- 输入 'quit' 或 'exit' 退出")
    console.print("- 输入 'meta' 切换元数据显示")
    
    show_metadata = False
    
    while True:
        try:
            # Get user input
            query_text = Prompt.ask("\n[bold cyan]🤖 请输入您的查询[/bold cyan]")
            
            if not query_text.strip():
                continue
            
            # Handle special commands
            if query_text.lower() in ['quit', 'exit', 'q']:
                console.print("[yellow]👋 再见![/yellow]")
                break
            elif query_text.lower() == 'help':
                console.print(Panel("""
[bold]可用命令:[/bold]
• help - 显示此帮助信息
• tools - 显示可用工具列表
• meta - 切换元数据显示
• quit/exit - 退出程序

[bold]查询示例:[/bold]
• 查询 node-01 的 CPU 使用率
• 显示内存使用情况
• 过去 1 小时的错误日志
""", title="帮助信息"))
                continue
            elif query_text.lower() == 'tools':
                display_available_tools(agent)
                continue
            elif query_text.lower() == 'meta':
                show_metadata = not show_metadata
                status = "开启" if show_metadata else "关闭"
                console.print(f"[yellow]元数据显示已{status}[/yellow]")
                continue
            
            # Process the query
            process_query(agent, query_text, show_metadata=show_metadata)
            
        except KeyboardInterrupt:
            console.print("\n[yellow]👋 再见![/yellow]")
            break
        except Exception as e:
            console.print(f"[red]❌ 发生错误: {e}[/red]")
            logger.error(f"Interactive mode error: {e}")


@app.command()
def info():
    """显示系统信息"""
    settings = setup_environment()
    
    info_table = Table(title="系统信息", show_header=True)
    info_table.add_column("配置项", style="cyan")
    info_table.add_column("值", style="white")
    
    info_table.add_row("应用名称", settings.app_name)
    info_table.add_row("版本", settings.app_version)
    info_table.add_row("OpenAI 模型", settings.openai_model)
    info_table.add_row("Prometheus URL", settings.prometheus_url)
    info_table.add_row("日志级别", settings.log_level)
    info_table.add_row("调试模式", "开启" if settings.debug else "关闭")
    
    console.print(info_table)
    
    # Test agent initialization
    try:
        agent = OpsAgentGraph()
        console.print("\n[green]✅ Agent 初始化成功[/green]")
        
        # Show graph structure
        console.print(Panel(agent.get_graph_visualization(), title="图结构", border_style="blue"))
        
    except Exception as e:
        console.print(f"\n[red]❌ Agent 初始化失败: {e}[/red]")


@app.command()
def demo():
    """运行演示查询"""
    settings = setup_environment()
    agent = OpsAgentGraph()
    
    display_welcome()
    
    demo_queries = [
        "查询 node-01 的 CPU 使用率过去 1 小时",
        "显示所有节点的内存使用情况",
        "过去 24 小时内哪些服务出现了错误",
        "重启 service-api 服务",
        "如何排查高 CPU 使用率问题？"
    ]
    
    console.print("[bold blue]🎬 运行演示查询...[/bold blue]\n")
    
    for i, query in enumerate(demo_queries, 1):
        console.print(f"[bold yellow]演示 {i}/{len(demo_queries)}[/bold yellow]")
        process_query(agent, query, show_metadata=True)
        
        if i < len(demo_queries):
            input("\n按 Enter 继续下一个演示...")


if __name__ == "__main__":
    # Default to interactive mode if no command provided
    if len(sys.argv) == 1:
        try:
            settings = setup_environment()
            agent = OpsAgentGraph()
            
            display_welcome()
            display_available_tools(agent)
            
            # Run interactive mode
            interactive()
        except KeyboardInterrupt:
            console.print("\n[yellow]👋 再见![/yellow]")
        except Exception as e:
            console.print(f"[red]❌ 启动失败: {e}[/red]")
            sys.exit(1)
    else:
        app()
