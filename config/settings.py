"""
Configuration settings for OpsGraph platform.

This module handles all configuration settings using Pydantic Settings.
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field
from functools import lru_cache


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = Field(default="OpsGraph", env="APP_NAME")
    app_version: str = Field(default="1.0.0", env="APP_VERSION")
    debug: bool = Field(default=False, env="DEBUG")
    
    # OpenAI settings
    openai_api_key: str = Field(default="sk-rIb6rm5o9VkNOBcKjIRIgKKRa3wnnjGG513qIPJ0svRvidxI", env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4-turbo-preview", env="OPENAI_MODEL")
    openai_temperature: float = Field(default=0.1, env="OPENAI_TEMPERATURE")
    openai_base_url: str = Field(default="https://api.b9349.dpdns.org", env="OPENAI_BASE_URL")
    
    # Prometheus settings
    prometheus_url: str = Field(default="http://localhost:9090", env="PROMETHEUS_URL")
    prometheus_timeout: int = Field(default=30, env="PROMETHEUS_TIMEOUT")
    
    # Logging settings
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    
    # Optional: Azure OpenAI settings
    azure_openai_api_key: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_KEY")
    azure_openai_endpoint: Optional[str] = Field(default=None, env="AZURE_OPENAI_ENDPOINT")
    azure_openai_api_version: Optional[str] = Field(default=None, env="AZURE_OPENAI_API_VERSION")
    
    # Optional: Anthropic settings
    anthropic_api_key: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    
    # Optional: Ollama settings
    ollama_base_url: Optional[str] = Field(default=None, env="OLLAMA_BASE_URL")
    ollama_model: Optional[str] = Field(default=None, env="OLLAMA_MODEL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()


def setup_logging(settings: Settings) -> None:
    """Setup logging configuration."""
    import logging
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format=settings.log_format,
        handlers=[
            logging.StreamHandler(),
        ]
    )
    
    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)


def validate_settings(settings: Settings) -> None:
    """Validate required settings."""
    if not settings.openai_api_key:
        raise ValueError("OPENAI_API_KEY is required")
    
    if not settings.prometheus_url:
        raise ValueError("PROMETHEUS_URL is required")
    
    # Validate log level
    valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
    if settings.log_level.upper() not in valid_log_levels:
        raise ValueError(f"LOG_LEVEL must be one of: {valid_log_levels}")


# Initialize settings on module import
try:
    _settings = get_settings()
    validate_settings(_settings)
    setup_logging(_settings)
except Exception as e:
    print(f"Warning: Failed to initialize settings: {e}")
    print("Please check your .env file and ensure all required variables are set.")
