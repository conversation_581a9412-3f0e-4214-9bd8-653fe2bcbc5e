# Core LangGraph and LangChain dependencies
langgraph>=0.2.0
langchain>=0.2.0
langchain-openai>=0.1.0
langchain-core>=0.2.0

# HTTP client for API calls
requests>=2.31.0
httpx>=0.25.0

# Prometheus client
prometheus-api-client>=0.5.3

# Environment and configuration management
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Async support
asyncio>=3.4.3

# Logging and utilities
loguru>=0.7.0
rich>=13.7.0

# CLI interface
typer>=0.9.0
click>=8.1.0

# Data processing
pandas>=2.1.0
numpy>=1.24.0

# Optional: FastAPI for web interface (future extension)
fastapi>=0.104.0
uvicorn>=0.24.0

# Development and testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
