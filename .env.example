# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_TEMPERATURE=0.1

# Prometheus Configuration
PROMETHEUS_URL=http://localhost:9090
PROMETHEUS_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# Application Configuration
APP_NAME=OpsGraph
APP_VERSION=1.0.0
DEBUG=false

# Optional: Alternative LLM providers
# AZURE_OPENAI_API_KEY=your_azure_openai_key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Optional: Anthropic Claude
# ANTHROPIC_API_KEY=your_anthropic_key

# Optional: Local LLM (Ollama)
# OLLAMA_BASE_URL=http://localhost:11434
# OLLAMA_MODEL=llama2
