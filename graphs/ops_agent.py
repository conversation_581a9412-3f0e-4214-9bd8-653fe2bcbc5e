"""
OpsAgent Graph implementation using LangGraph.

This module defines the main agent workflow graph that orchestrates the entire process:
UserQuery → LLMRouter → Tool Layer → LLMInterpreter → Response
"""

import logging
from typing import Dict, Any, List, Optional
from typing_extensions import TypedDict

from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage

from agents.router import LLMRouter, ToolType
from agents.interpreter import LLMInterpreter
from tools.prometheus_tool import PrometheusTool
from tools.placeholder_tools import LogTool, CICDTool, K8sTool, AutomationTool, KnowledgeTool

logger = logging.getLogger(__name__)


class AgentState(TypedDict):
    """State definition for the OpsAgent graph."""
    # Input
    user_query: str
    
    # Router output
    selected_tool: Optional[str]
    tool_confidence: Optional[float]
    routing_reasoning: Optional[str]
    processed_query: Optional[str]
    
    # Tool output
    tool_results: Optional[str]
    tool_error: Optional[str]
    
    # Interpreter output
    final_response: Optional[str]
    
    # Metadata
    messages: List[BaseMessage]
    execution_path: List[str]
    timestamp: Optional[str]


class OpsAgentGraph:
    """Main OpsAgent graph that orchestrates the entire workflow."""
    
    def __init__(self):
        """Initialize the OpsAgent graph with all components."""
        # Initialize agents
        self.router = LLMRouter()
        self.interpreter = LLMInterpreter()
        
        # Initialize tools
        self.tools = {
            ToolType.PROMETHEUS.value: PrometheusTool(),
            ToolType.LOG.value: LogTool(),
            ToolType.CICD.value: CICDTool(),
            ToolType.K8S.value: K8sTool(),
            ToolType.AUTOMATION.value: AutomationTool(),
            ToolType.KNOWLEDGE.value: KnowledgeTool(),
        }
        
        # Build the graph
        self.graph = self._build_graph()
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph workflow."""
        
        # Create the graph
        workflow = StateGraph(AgentState)
        
        # Add nodes
        workflow.add_node("router", self._router_node)
        workflow.add_node("tool_executor", self._tool_executor_node)
        workflow.add_node("interpreter", self._interpreter_node)
        
        # Define the flow
        workflow.set_entry_point("router")
        workflow.add_edge("router", "tool_executor")
        workflow.add_edge("tool_executor", "interpreter")
        workflow.add_edge("interpreter", END)
        
        return workflow.compile()
    
    def _router_node(self, state: AgentState) -> AgentState:
        """Router node: Analyze query and select appropriate tool."""
        try:
            logger.info("Executing router node")
            
            user_query = state["user_query"]
            decision = self.router.route_query(user_query)
            
            # Update state with routing decision
            state["selected_tool"] = decision.tool_type.value
            state["tool_confidence"] = decision.confidence
            state["routing_reasoning"] = decision.reasoning
            state["processed_query"] = decision.processed_query
            state["execution_path"].append("router")
            
            logger.info(f"Router selected tool: {decision.tool_type.value} "
                       f"(confidence: {decision.confidence})")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in router node: {str(e)}")
            # Fallback to Prometheus tool
            state["selected_tool"] = ToolType.PROMETHEUS.value
            state["tool_confidence"] = 0.1
            state["routing_reasoning"] = f"Router error, fallback to Prometheus: {str(e)}"
            state["processed_query"] = state["user_query"]
            state["execution_path"].append("router_error")
            return state
    
    def _tool_executor_node(self, state: AgentState) -> AgentState:
        """Tool executor node: Execute the selected tool."""
        try:
            logger.info("Executing tool executor node")
            
            selected_tool = state["selected_tool"]
            processed_query = state["processed_query"]
            
            if selected_tool not in self.tools:
                raise ValueError(f"Unknown tool: {selected_tool}")
            
            # Execute the tool
            tool = self.tools[selected_tool]
            logger.info(f"Executing tool: {selected_tool}")
            
            tool_results = tool._run(processed_query)
            
            # Update state with tool results
            state["tool_results"] = tool_results
            state["execution_path"].append(f"tool_{selected_tool}")
            
            logger.info(f"Tool {selected_tool} executed successfully")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in tool executor node: {str(e)}")
            state["tool_error"] = str(e)
            state["tool_results"] = f'{{"error": "{str(e)}", "status": "failed"}}'
            state["execution_path"].append("tool_error")
            return state
    
    def _interpreter_node(self, state: AgentState) -> AgentState:
        """Interpreter node: Interpret tool results and generate final response."""
        try:
            logger.info("Executing interpreter node")
            
            user_query = state["user_query"]
            selected_tool = state["selected_tool"]
            tool_results = state["tool_results"]
            
            # Generate interpretation
            final_response = self.interpreter.interpret_results(
                original_query=user_query,
                tool_name=selected_tool,
                tool_results=tool_results,
                include_raw_data=False
            )
            
            # Update state with final response
            state["final_response"] = final_response
            state["execution_path"].append("interpreter")
            
            logger.info("Interpreter generated final response")
            
            return state
            
        except Exception as e:
            logger.error(f"Error in interpreter node: {str(e)}")
            # Generate basic error response
            state["final_response"] = f"""
## Error Processing Query

**Query:** {state['user_query']}
**Tool:** {state.get('selected_tool', 'unknown')}
**Error:** {str(e)}

The system encountered an error while interpreting the results. 
Please try rephrasing your question or contact support.

**Raw Tool Output:**
{state.get('tool_results', 'No tool results available')}
"""
            state["execution_path"].append("interpreter_error")
            return state
    
    def invoke(self, user_query: str) -> Dict[str, Any]:
        """Invoke the OpsAgent graph with a user query."""
        try:
            logger.info(f"Invoking OpsAgent with query: {user_query}")
            
            # Initialize state
            initial_state = AgentState(
                user_query=user_query,
                selected_tool=None,
                tool_confidence=None,
                routing_reasoning=None,
                processed_query=None,
                tool_results=None,
                tool_error=None,
                final_response=None,
                messages=[],
                execution_path=[],
                timestamp=None
            )
            
            # Execute the graph
            final_state = self.graph.invoke(initial_state)
            
            # Prepare response
            response = {
                "query": user_query,
                "response": final_state.get("final_response", "No response generated"),
                "metadata": {
                    "selected_tool": final_state.get("selected_tool"),
                    "tool_confidence": final_state.get("tool_confidence"),
                    "routing_reasoning": final_state.get("routing_reasoning"),
                    "execution_path": final_state.get("execution_path", []),
                    "has_error": final_state.get("tool_error") is not None
                }
            }
            
            logger.info("OpsAgent execution completed successfully")
            return response
            
        except Exception as e:
            logger.error(f"Error invoking OpsAgent: {str(e)}")
            return {
                "query": user_query,
                "response": f"System error: {str(e)}",
                "metadata": {
                    "selected_tool": None,
                    "tool_confidence": 0.0,
                    "routing_reasoning": f"System error: {str(e)}",
                    "execution_path": ["error"],
                    "has_error": True
                }
            }
    
    async def ainvoke(self, user_query: str) -> Dict[str, Any]:
        """Async version of invoke."""
        # For now, just call the sync version
        # In a real implementation, you'd want to make this truly async
        return self.invoke(user_query)
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools."""
        return self.router.get_available_tools()
    
    def get_graph_visualization(self) -> str:
        """Get a text representation of the graph structure."""
        return """
OpsAgent Graph Structure:

┌─────────────┐
│ User Query  │
└─────┬───────┘
      │
      ▼
┌─────────────┐
│ LLM Router  │ ──── Intent Recognition & Tool Selection
└─────┬───────┘
      │
      ▼
┌─────────────┐
│Tool Executor│ ──── Execute Selected Tool
│             │      • PrometheusTool
│             │      • LogTool (placeholder)
│             │      • CICDTool (placeholder)
│             │      • K8sTool (placeholder)
│             │      • AutomationTool (placeholder)
│             │      • KnowledgeTool (placeholder)
└─────┬───────┘
      │
      ▼
┌─────────────┐
│LLMInterpreter│ ──── Result Interpretation & Summary
└─────┬───────┘
      │
      ▼
┌─────────────┐
│  Response   │
└─────────────┘
"""
